{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist", "rootDir": "./", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ES2020", "DOM", "DOM.Iterable"]}, "files": ["engine/src/avatar/components/AIAnimationSynthesisComponent.ts", "engine/src/avatar/components/FacialAnimationComponent.ts", "engine/src/avatar/components/LipSyncComponent.ts"]}